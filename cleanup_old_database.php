<?php

// สคริปต์สำหรับลบฐานข้อมูลเดิม (ใช้เมื่อแน่ใจแล้วว่าทุกอย่างทำงานปกติ)

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

$oldDbName = 'soloshop';

echo "⚠️  คำเตือน: สคริปต์นี้จะลบฐานข้อมูลเดิม '$oldDbName' ถาวร!\n";
echo "กรุณาตรวจสอบให้แน่ใจว่าฐานข้อมูลใหม่ทำงานปกติแล้ว\n\n";

echo "พิมพ์ 'DELETE' เพื่อยืนยันการลบ: ";
$confirmation = trim(fgets(STDIN));

if ($confirmation === 'DELETE') {
    try {
        echo "\n🗑️  กำลังลบฐานข้อมูลเดิม '$oldDbName'...\n";
        
        DB::statement("DROP DATABASE IF EXISTS `$oldDbName`");
        
        echo "✅ ลบฐานข้อมูลเดิมสำเร็จ!\n";
        echo "🎉 การเปลี่ยนชื่อฐานข้อมูลเสร็จสิ้นสมบูรณ์!\n";
        
    } catch (Exception $e) {
        echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ ยกเลิกการลบฐานข้อมูล\n";
}

echo "\n📝 หมายเหตุ: คุณสามารถลบไฟล์นี้ได้หลังจากใช้งานแล้ว\n";
