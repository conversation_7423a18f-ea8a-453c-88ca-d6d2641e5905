<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Service;
use App\Models\Package;
use App\Models\Contact;
use App\Models\Activity;
use App\Models\SiteSetting;

class HomeController extends Controller
{
    public function index()
    {
        $services = Service::orderBy('sort_order', 'asc')->orderBy('created_at', 'asc')->take(6)->get();
        $activities = Activity::with('images')->where('is_published', 1)->inRandomOrder()->take(4)->get();
        $settings = $this->getSettings();

        return view('frontend.home', compact('services', 'activities', 'settings'));
    }

    public function services()
    {
        $services = Service::orderBy('sort_order', 'asc')->orderBy('created_at', 'asc')->get();
        $settings = $this->getSettings();

        return view('frontend.services', compact('services', 'settings'));
    }

    public function packages()
    {
        $packages = Package::orderBy('sort_order', 'asc')->orderBy('created_at', 'asc')->get();
        $settings = $this->getSettings();

        return view('frontend.packages', compact('packages', 'settings'));
    }

    public function activities()
    {
        $activities = Activity::with('images')->where('is_published', 1)->orderBy('created_at', 'desc')->get();
        $settings = $this->getSettings();

        return view('frontend.activities', compact('activities', 'settings'));
    }

    public function showActivity($id)
    {
        $activity = Activity::with('images')->active()->findOrFail($id);
        $relatedActivities = Activity::with('images')->where('is_published', 1)
            ->where('id', '!=', $id)
            ->inRandomOrder()
            ->take(4)
            ->get();
        $settings = $this->getSettings();

        return view('frontend.activity-detail', compact('activity', 'relatedActivities', 'settings'));
    }

    public function contact()
    {
        $settings = $this->getSettings();

        return view('frontend.contact', compact('settings'));
    }

    public function storeContact(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string'
        ]);

        Contact::create($request->all());

        return redirect()->route('contact')->with('success', 'ข้อความของคุณถูกส่งเรียบร้อยแล้ว เราจะติดต่อกลับโดยเร็วที่สุด');
    }

    private function getSettings()
    {
        $settings = \DB::table('site_settings')->first();

        return [
            'site_name' => $settings->site_name ?? 'ผู้ใหญ่ประจักษ์ เซอร์วิส ช็อป',
            'site_description' => $settings->site_description ?? 'ผู้เชี่ยวชาญด้านการให้บริการที่ครบครันและมีคุณภาพ',
            'contact_phone' => $settings->contact_phone ?? '02-123-4567',
            'contact_email' => $settings->contact_email ?? '<EMAIL>',
            'contact_address' => $settings->contact_address ?? '123 ถนนสุขุมวิท แขวงคลองเตย เขตคลองเตย กรุงเทพฯ 10110',
            'facebook_url' => $settings->facebook_url ?? 'https://facebook.com/phuyaiprajak',
            'line_id' => $settings->line_url ?? '@phuyaiprajak',
        ];
    }
}
