<?php $__env->startSection('title', 'หน้าหลัก - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ')); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section - สวยงามเรียบง่าย -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center min-vh-75">
            <div class="col-lg-6 mb-5 mb-lg-0">
                <div class="hero-content">
                    <h1 class="display-4 mb-4"><?php echo e($settings['site_name'] ?? 'ผู้ใหญ่ประจักษ์ เซอร์วิส ช็อป'); ?></h1>
                    <p class="lead mb-5"><?php echo e($settings['site_description'] ?? 'บริการที่ดีที่สุด ด้วยความใส่ใจและคุณภาพ'); ?></p>
                    <div class="d-flex flex-column flex-sm-row gap-3">
                        <a href="<?php echo e(route('services')); ?>" class="btn btn-primary btn-lg">
                            <i class="fas fa-arrow-right me-2"></i>ดูบริการของเรา
                        </a>
                        <a href="<?php echo e(route('contact')); ?>" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-phone me-2"></i>ติดต่อเรา
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-image text-center">
                    <div class="hero-icon-container">
                        <i class="fas fa-star fa-8x text-primary opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Section - เรียบง่ายสวยงาม -->
<?php if($services->count() > 0): ?>
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">บริการของเรา</h2>
            <p class="section-subtitle">บริการที่ครอบคลุมและมีคุณภาพ พร้อมดูแลคุณด้วยความใส่ใจ</p>
        </div>

        <div class="row g-4">
            <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-6 col-lg-4">
                <div class="card service-card h-100">
                    <?php if($service->image): ?>
                    <div class="card-image-container img-size-medium">
                        <img src="<?php echo e(asset('storage/' . $service->image)); ?>" class="img-fit-contain" alt="<?php echo e($service->title); ?>">
                    </div>
                    <?php else: ?>
                    <div class="card-image-container img-size-medium bg-light d-flex align-items-center justify-content-center">
                        <i class="fas fa-praying-hands fa-3x text-muted"></i>
                    </div>
                    <?php endif; ?>

                    <div class="card-body">
                        <h5 class="card-title"><?php echo e($service->title); ?></h5>
                        <p class="card-text"><?php echo e(Str::limit($service->description, 100)); ?></p>
                        <div class="text-center mt-3">
                            <small class="text-muted">สอบถามราคาได้ที่เจ้าหน้าที่</small>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <div class="text-center mt-5">
            <a href="<?php echo e(route('services')); ?>" class="btn btn-primary btn-lg">ดูบริการทั้งหมด</a>
        </div>
    </div>
</section>
<?php endif; ?>



<!-- Activities Section - แกลเลอรี่สวยงาม -->
<?php if($activities->count() > 0): ?>
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-title">ผลงานของเรา</h2>
            <p class="section-subtitle">ภาพบรรยากาศการให้บริการที่ผ่านมา แสดงถึงคุณภาพและความใส่ใจ</p>
        </div>

        <div class="row g-4">
            <?php $__currentLoopData = $activities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-6 col-lg-3">
                <div class="card service-card h-100 activity-card-home" style="cursor: pointer;">
                    <div class="card-image-container img-size-medium">
                        <?php
                            $homeCoverImage = $activity->images->where('is_cover', true)->first() ?? $activity->images->first();
                            $homeCoverImagePath = $homeCoverImage ? $homeCoverImage->image_path : $activity->image;
                        ?>
                        <img src="<?php echo e(asset('storage/' . $homeCoverImagePath)); ?>"
                             class="img-fit-contain"
                             alt="<?php echo e($activity->title); ?>">
                        <div class="position-absolute top-0 start-0 w-100 h-100 bg-dark bg-opacity-25 d-flex align-items-center justify-content-center opacity-0 home-activity-overlay">
                            <div class="text-center text-white">
                                <i class="fas fa-eye fa-lg mb-1"></i>
                                <div class="small">ดูรายละเอียด</div>
                                <?php if($activity->images->count() > 1): ?>
                                <div class="small"><?php echo e($activity->images->count()); ?> รูป</div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <h6 class="card-title"><?php echo e($activity->title); ?></h6>
                        <p class="card-text small"><?php echo e(Str::limit($activity->description, 80)); ?></p>
                        <div class="d-flex align-items-center text-muted small">
                            <i class="fas fa-calendar me-2"></i>
                            <?php echo e($activity->activity_date->format('d/m/Y')); ?>

                        </div>
                    </div>
                    <a href="<?php echo e(route('activities.show', $activity->id)); ?>" class="stretched-link"></a>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <div class="text-center mt-5">
            <a href="<?php echo e(route('activities')); ?>" class="btn btn-primary btn-lg">ดูผลงานทั้งหมด</a>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Contact CTA Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="fw-bold mb-4">ต้องการความช่วยเหลือหรือไม่?</h2>
                <p class="lead mb-4">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก ติดต่อเราได้ตลอด 24 ชั่วโมง</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary btn-lg">
                        <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                    </a>
                    <a href="tel:<?php echo e($settings['contact_phone'] ?? ''); ?>" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-phone me-2"></i><?php echo e($settings['contact_phone'] ?? '02-xxx-xxxx'); ?>

                    </a>
                </div>
                <div class="mt-4">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        บริการตลอด 24 ชั่วโมง ทุกวัน
                    </small>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/frontend/home.blade.php ENDPATH**/ ?>